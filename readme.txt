demo name : can_demo
demo ver : sv01.001
first edition compilation time :2022/01/11

note:

1)different platforms have to choose different cross-compilation .you need to modify makefile redefine "CC".

2)can2.0 bitrate Kbps default:250K,range :5，10，20，40，50，80，100，125，200，250，400，500，666，800，1000,2000,4000,5000

3)canfd bitrate Kbps 5k to 5M ,default:250K,range :5，10，20，40，50，80，100，125，200，250，400，500，666，800，1000,2000,4000,5000
        dbitrate Kbps 250k to 5M,default:250K,range :250，500，1000,2000,4000,5000,8000
        dbitrate must greater than bitrate
        recommend dbitrate is 1-8 times bitrate.

4)The initiating test device regularly sends the specified data, and then waits for the response device to receive the data back to verify whether the received data is consistent with the sent data. 
Inconsistent will be red letter error reminder, consistent do not print information. 
After all test times, the error-free green letter is successful, otherwise the red letter fails.

some more examples:
