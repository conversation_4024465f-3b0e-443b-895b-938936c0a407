#!/bin/bash

# 测试脚本：分析网络命令是否访问 RTC
# 基于 can_performance 程序中使用的命令

echo "=== 测试开始 ==="
echo "监控 /dev/rtc0 的访问情况"

# 函数：执行命令并监控 RTC 访问
test_command() {
    local cmd="$1"
    echo ""
    echo "测试命令: $cmd"
    echo "--- strace 输出 ---"
    
    # 使用 strace 跟踪文件访问，过滤 rtc 相关
    strace -e trace=openat,open,read,write -f $cmd 2>&1 | grep -i rtc || echo "未发现 RTC 访问"
    
    echo "--- lsof 检查 ---"
    # 检查命令执行后是否有进程打开 rtc
    lsof | grep rtc || echo "未发现进程打开 RTC"
}

# 设置测试设备（如果不存在 can0，可能需要加载模块）
DEVICE="can0"

echo "当前系统中的 RTC 设备："
ls -la /dev/rtc* 2>/dev/null || echo "未找到 RTC 设备"

echo ""
echo "当前打开 RTC 的进程："
lsof | grep rtc || echo "未发现进程打开 RTC"

# 测试程序中使用的命令序列
echo ""
echo "=== 测试 CAN 程序中的命令序列 ==="

# 1. 测试 ifconfig down
test_command "ifconfig $DEVICE down"

# 2. 测试 ip link set 命令
test_command "ip link set $DEVICE up type can bitrate 250000 sample-point 0.75"

# 3. 测试设置队列长度
test_command "ip link set $DEVICE txqueuelen 1000"

# 4. 测试 ifconfig up
test_command "ifconfig $DEVICE up"

echo ""
echo "=== 测试完成 ==="
echo "如果上述命令中有 RTC 访问，会在对应的 strace 输出中显示"
